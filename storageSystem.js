// src/services/storageSystem.js
import { nhost } from './nhost';
import { gql } from '@apollo/client'; // Direct import for defining queries/mutations
import axios from 'axios'; // For direct ComfyUI communication

// --- Configuration ---
const WORKER_BASE_URL = '/api'; // Assuming Cloudflare Pages proxy at /api
const comfyInstanceCache = {}; // Simple in-memory cache for Comfy URLs

// --- GraphQL Definitions (Using user_wallet) ---

// Query to get ComfyUI server URL based on toolId
const GET_COMFY_SERVER_URL_QUERY = gql`
  query GetComfyServerUrl($toolId: String!) {
    # Target the actual comfyui_servers table
    comfyui_servers(where: { tool_id: { _eq: $toolId } }) {
      url
    }
  }
`;

// Mutation to log a prompt attempt
const INSERT_PROMPT_LOG_MUTATION = gql`
  mutation InsertPromptLog(
    $userId: uuid!
    $toolId: String!
    $originalFileName: String
    $promptText: String
    $workflowIdentifier: String
    $status: String
  ) {
    # Target the prompts table
    insert_prompts_one(
      object: {
        user_id: $userId
        tool_id: $toolId
        original_file_name: $originalFileName
        prompt_text: $promptText
        workflow_identifier: $workflowIdentifier
        status: $status
        # timestamp is set by default in Hasura/Postgres
      }
    ) {
      id # Return the ID of the newly inserted prompt log
    }
  }
`;

// Mutation to save project metadata
const INSERT_PROJECT_MUTATION = gql`
  mutation InsertProject(
    $userId: uuid!
    $promptId: String! # ComfyUI prompt ID
    $outputUrl: String!
    $outputR2Key: String!
    $toolId: String!
    $originalFileName: String
    $promptUsed: String
    $contentType: String
    $comfyOutputFileName: String
    $outputType: String # 'image' or 'video'
    # Add fields from refined schema if needed here (e.g., credit_cost)
    # $creditCost: Int
    # $generationType: String
  ) {
     # Target the projects table
    insert_projects_one(
      object: {
        user_id: $userId
        prompt_id: $promptId
        output_url: $outputUrl
        output_r2_key: $outputR2Key
        tool_id: $toolId
        original_file_name: $originalFileName
        prompt_used: $promptUsed
        content_type: $contentType
        comfy_output_file_name: $comfyOutputFileName
        output_type: $outputType
        # credit_cost: $creditCost # Add if sending cost from client
        # generation_type: $generationType
        # timestamp and savedAt can be set by default in Hasura/Postgres
      }
    ) {
      id # Return the ID of the newly inserted project
    }
  }
`;

// --- Service Functions ---

/**
 * Fetches the ComfyUI instance URL for a specific tool using GraphQL via nhost.graphql.request().
 * Caches the result in memory.
 * @param {string} toolId - The ID of the tool (e.g., 'reimagine', 'img2video').
 * @returns {Promise<string>} The ComfyUI instance URL.
 * @throws {Error} If the query fails or the URL is not found/invalid.
 */
async function getComfyInstanceUrl(toolId) {
  if (comfyInstanceCache[toolId]) {
    console.log(`[StorageSystem] Using cached ComfyUI URL for ${toolId}`);
    return comfyInstanceCache[toolId];
  }
  console.log(`[StorageSystem] Fetching ComfyUI URL for tool: ${toolId} via nhost.graphql.request()...`);

  try {
    // --- Use nhost.graphql.request() instead of getClient() ---
    const { data, error } = await nhost.graphql.request(
        GET_COMFY_SERVER_URL_QUERY, // Pass the gql query object
        { toolId: toolId }          // Pass variables
        // Note: nhost.graphql.request() handles authentication automatically
    );

    // --- Handle potential GraphQL errors ---
    if (error) {
      // nhost.graphql.request wraps errors slightly differently
      const errorMessage = Array.isArray(error.errors) ? error.errors[0]?.message : error.message || 'Unknown GraphQL error';
      const errorCode = Array.isArray(error.errors) ? error.errors[0]?.extensions?.code : error.code;

      console.error('[StorageSystem] GraphQL error fetching ComfyUI URL:', JSON.stringify(error, null, 2));

      // Add specific checks if needed
      if (errorMessage.includes('permission') || errorCode === 'permission-error') {
          console.error("[StorageSystem] Detailed Error: Permission denied. Check Hasura permissions for 'user' role on 'comfyui_servers' table (select).");
      } else if (errorCode === 'validation-failed') {
           console.error("[StorageSystem] Detailed Error: Validation failed. Check query syntax and Hasura schema.");
      }
      // Note: Persisted Query errors are less likely with .request() but keep check if needed
      // else if (errorMessage.includes('PersistedQuery')) { ... }

      throw new Error(`GraphQL query failed: ${errorMessage}`);
    }

    // --- Process Successful Response ---
    // Access data directly from the 'data' property
    const server = data?.comfyui_servers?.[0];
    const url = server?.url;

    if (!url || typeof url !== 'string' || url.trim() === '') {
      console.error(`[StorageSystem] Invalid URL data retrieved for tool '${toolId}':`, server, "Data:", data);
      throw new Error(`URL not found or invalid in backend for toolId '${toolId}'. Check Hasura 'comfyui_servers' data and permissions.`);
    }

    console.log(`[StorageSystem] Fetched & cached ComfyUI URL for ${toolId}: ${url}`);
    comfyInstanceCache[toolId] = url; // Cache it
    return url;

  } catch (err) {
    // Catch errors from nhost.graphql.request() or processing
    console.error(`[StorageSystem] Failed to execute getComfyInstanceUrl for ${toolId}:`, err);
    // Propagate a user-friendly error message, ensuring it's an Error object
    if (err instanceof Error) {
        throw new Error(`Could not get Comfy URL. ${err.message}`);
    } else {
        throw new Error(`Could not get Comfy URL. An unknown error occurred.`);
    }
  }
}

/**
 * Fetches the workflow JSON template from Nhost Storage using a presigned URL.
 * @param {string} workflowFileId - The File ID from Nhost Storage.
 * @returns {Promise<object>} The parsed workflow JSON object.
 * @throws {Error} If fetching or parsing fails, or if permission is denied.
 */
async function getWorkflowTemplate(workflowFileId) {
  if (!workflowFileId || workflowFileId.startsWith('YOUR_NHOST_STORAGE_FILE_ID')) {
      // Added check for placeholder
      console.error("[StorageSystem] Invalid or placeholder workflowFileId provided:", workflowFileId);
      throw new Error("Workflow File ID is invalid or not configured.");
  }
  console.log(`[StorageSystem] Fetching workflow template from Nhost Storage, File ID: ${workflowFileId}`);
  try {
    const { presignedUrl, error: urlError } = await nhost.storage.getPresignedUrl({
      fileId: workflowFileId,
    });

    if (urlError) {
      console.error('[StorageSystem] Error getting presigned URL:', urlError);
      throw new Error(urlError.message || 'Could not get workflow URL');
    }
    if (!presignedUrl?.url) {
         throw new Error('Presigned URL generation failed.');
    }

    console.log(`[StorageSystem] Obtained presigned workflow URL.`);
    const response = await fetch(presignedUrl.url);
    if (!response.ok) {
      if (response.status === 404) throw new Error(`Workflow file not found in Nhost Storage (ID: ${workflowFileId})`);
      if (response.status === 403) throw new Error(`Permission denied for workflow file (ID: ${workflowFileId}). Check Nhost Storage rules.`);
      throw new Error(`Workflow download failed (${response.status}): ${response.statusText}`);
    }
    const template = await response.json();
    console.log('[StorageSystem] Workflow template fetched and parsed successfully.');
    return template;
  } catch (error) {
    console.error(`[StorageSystem] Error fetching workflow [FileID: ${workflowFileId}]:`, error);
    throw new Error(`Could not load workflow: ${error.message}`);
  }
}


/**
 * Uploads the user's input file directly to the specified ComfyUI instance.
 * @param {File} file - The File object to upload.
 * @param {string} comfyInstanceUrl - The specific ComfyUI instance URL.
 * @returns {Promise<string>} The temporary filename assigned by ComfyUI.
 * @throws {Error} If the upload fails.
 */
async function uploadInputToComfy(file, comfyInstanceUrl) {
    if (!file) throw new Error("[StorageSystem] No input file provided.");
    if (!comfyInstanceUrl) throw new Error("[StorageSystem] ComfyUI instance URL is required.");

    const comfyUploadUrl = `${comfyInstanceUrl}/upload/image`;
    const formData = new FormData();
    formData.append('image', file, file.name);
    console.log(`[StorageSystem] Uploading input file '${file.name}' to: ${comfyUploadUrl}`);
    try {
        const response = await axios.post(comfyUploadUrl, formData, { timeout: 90000 });
        if (!response.data?.name) throw new Error('ComfyUI upload response missing filename field.');
        console.log(`[StorageSystem] Input uploaded to ComfyUI as: ${response.data.name}`);
        return response.data.name;
    } catch (error) {
        console.error(`[StorageSystem] Direct ComfyUI Upload Error [${comfyUploadUrl}]:`, error);
        const message = error.response?.data?.message || error.message;
        if (error.code === 'ERR_NETWORK' || message.includes('CORS')) {
            throw new Error(`Network/CORS Error uploading to ComfyUI at ${comfyInstanceUrl}. Check server status & CORS policy.`);
        }
        throw new Error(`ComfyUI Upload Failed: ${message}`);
    }
}

/**
 * Logs prompt details to the Nhost backend using a GraphQL mutation.
 * @param {string} userId - Nhost User ID.
 * @param {string} toolId - Tool identifier ('reimagine', etc.).
 * @param {string} originalFileName - Name of the user's original input file.
 * @param {string} promptText - The text prompt used (or default).
 * @param {string} workflowIdentifier - File ID or path of the workflow structure used.
 * @returns {Promise<string|null>} ID of the created prompt log entry, or null if logging failed non-critically.
 */
async function logPromptAttempt(userId, toolId, originalFileName, promptText, workflowIdentifier) {
  if (!userId) {
    console.warn('[StorageSystem] Cannot log prompt attempt without userId.');
    return null;
  }
  console.log(`[StorageSystem] Logging prompt attempt for user ${userId}, tool ${toolId} via GraphQL...`);
  try {
    // Use nhost.graphql.request for simple mutations
    const { data, error } = await nhost.graphql.request(INSERT_PROMPT_LOG_MUTATION, {
      userId: userId,
      toolId: toolId,
      originalFileName: originalFileName,
      promptText: promptText,
      workflowIdentifier: workflowIdentifier,
      status: 'submitted',
    });

    if (error) {
      console.warn('[StorageSystem] GraphQL mutation error logging prompt:', error);
      return null;
    }
    const logId = data?.insert_prompts_one?.id;
    if (logId) {
      console.log('[StorageSystem] Prompt log saved via GraphQL, ID:', logId);
      return logId;
    } else {
      console.warn('[StorageSystem] Prompt log mutation succeeded but returned no ID.');
      return null;
    }
  } catch (err) {
    console.warn('[StorageSystem] Unexpected error logging prompt:', err);
    return null;
  }
}

/**
 * Submits the prepared workflow payload to the specific ComfyUI instance.
 * @param {object} workflowPayload - The complete workflow JSON object.
 * @param {string} comfyInstanceUrl - The specific ComfyUI URL.
 * @returns {Promise<string>} The ComfyUI prompt_id.
 * @throws {Error} If the submission fails.
 */
async function submitWorkflowToComfy(workflowPayload, comfyInstanceUrl) {
    if (!workflowPayload) throw new Error("[StorageSystem] Workflow payload is required.");
    if (!comfyInstanceUrl) throw new Error("[StorageSystem] ComfyUI instance URL is required.");

    const payload = { prompt: workflowPayload };
    const comfySubmitUrl = `${comfyInstanceUrl}/prompt`;
    console.log(`[StorageSystem] Submitting workflow to: ${comfySubmitUrl}`);
    try {
        const response = await axios.post(comfySubmitUrl, payload, {
            headers: { 'Content-Type': 'application/json' },
            timeout: 30000
        });
        if (!response.data?.prompt_id) throw new Error('ComfyUI submit response did not contain prompt_id.');
        console.log(`[StorageSystem] Workflow submitted. Prompt ID: ${response.data.prompt_id}`);
        return response.data.prompt_id;
    } catch (error) {
        console.error(`[StorageSystem] ComfyUI Submit Error [${comfySubmitUrl}]:`, error);
        const message = error.response?.data?.message || error.message;
        throw new Error(`ComfyUI workflow submission failed: ${message}`);
    }
}

/**
 * Polls the specified ComfyUI instance for the result of a given prompt ID.
 * Once the output filename is found, triggers the Cloudflare Worker to fetch it,
 * save it to R2, and return R2 details.
 * Includes a bypass for local development.
 * @param {string} promptId - The ComfyUI prompt ID to poll.
 * @param {string} toolId - Tool ID (passed to worker for context/logging).
 * @param {string} comfyInstanceUrl - The specific ComfyUI instance URL where the job ran.
 * @returns {Promise<object>} Object containing { r2PublicUrl, r2OutputKey, contentType, comfyOutputFileName }.
 * @throws {Error} If polling times out, the worker call fails, or authentication is missing.
 */
async function pollComfyOutputAndSave(promptId, toolId, comfyInstanceUrl) {
    const user = nhost.auth.getUser();
    if (!user) throw new Error("[StorageSystem] Authentication required before polling/saving.");
    if (!promptId || !toolId || !comfyInstanceUrl) throw new Error("[StorageSystem] Missing required arguments for polling/saving.");

    const historyUrl = `${comfyInstanceUrl}/history/${promptId}`;
    console.log(`[StorageSystem] Polling ComfyUI history: ${historyUrl}`);
    let attempt = 0;
    const maxAttempts = 1000; // ~15 minutes
    const pollInterval = 3000; // 3 seconds

    while (attempt < maxAttempts) {
        attempt++;
        try {
            await new Promise((resolve) => setTimeout(resolve, pollInterval));
            const historyResponse = await axios.get(historyUrl, { timeout: 15000 });
            const historyData = historyResponse.data;

            if (historyData?.[promptId]?.outputs) {
                const outputs = historyData[promptId].outputs;
                for (const nodeId in outputs) {
                    const nodeOutput = outputs[nodeId];
                    const images = nodeOutput.images || nodeOutput.gifs; // Handle images or gifs
                    if (images?.[0]?.filename) {
                        const comfyOutputFileName = images[0].filename;
                        const outputType = images[0].type || 'output';
                        const subfolder = images[0].subfolder || '';
                        const fullComfyPath = subfolder ? `${subfolder}/${comfyOutputFileName}` : comfyOutputFileName;
                        const presumedContentType = images[0].format || (comfyOutputFileName.endsWith('mp4') ? 'video/mp4' : 'image/png');

                        console.log(`[StorageSystem] ComfyUI output found: ${fullComfyPath} (Type: ${outputType})`);

                        // --- LOCAL DEVELOPMENT BYPASS ---
                        if (import.meta.env.DEV) {
                            console.warn("[StorageSystem] --- LOCAL DEV MODE: Skipping R2 Upload ---");
                            const comfyViewUrl = `${comfyInstanceUrl}/view?filename=${encodeURIComponent(comfyOutputFileName)}&type=${outputType}&subfolder=${encodeURIComponent(subfolder)}`;
                            return {
                                r2PublicUrl: comfyViewUrl,
                                r2OutputKey: `local-dev/${toolId}/${fullComfyPath}`,
                                contentType: presumedContentType,
                                comfyOutputFileName: fullComfyPath,
                            };
                        }
                        // --- PRODUCTION: Trigger Cloudflare Worker ---
                        else {
                            console.log("[StorageSystem] Production: Triggering worker to fetch/save to R2...");
                            const session = await nhost.auth.refreshSession(); // Get fresh session/JWT
                            if (!session?.accessToken) throw new Error("Failed to get authentication token for worker.");

                            const workerUrl = `${WORKER_BASE_URL}/save-output`;
                            const workerResponse = await fetch(workerUrl, {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                    'Authorization': `Bearer ${session.accessToken}` // Use Nhost JWT
                                },
                                body: JSON.stringify({
                                    comfyFileName: comfyOutputFileName,
                                    comfyInstanceUrl: comfyInstanceUrl,
                                    comfyOutputType: outputType,
                                    comfySubfolder: subfolder,
                                    toolId: toolId,
                                    promptId: promptId
                                })
                            });

                            if (!workerResponse.ok) {
                                const errorText = await workerResponse.text();
                                console.error(`[StorageSystem] Worker Error Response (${workerResponse.status}):`, errorText);
                                throw new Error(`Worker failed (${workerResponse.status}): ${errorText || 'Unknown worker error'}`);
                            }
                            const resultData = await workerResponse.json();
                            if (!resultData?.r2PublicUrl || !resultData?.r2OutputKey) {
                                console.error("[StorageSystem] Invalid response from worker:", resultData);
                                throw new Error("Worker response missing required R2 details.");
                            }
                            console.log("[StorageSystem] Worker save successful:", resultData);
                            return {
                                r2PublicUrl: resultData.r2PublicUrl,
                                r2OutputKey: resultData.r2OutputKey,
                                contentType: resultData.contentType,
                                comfyOutputFileName: fullComfyPath,
                             };
                        } // End production block
                    } // end if filename found
                } // end loop through output nodes
            } // end if history outputs exist

            console.log(`[StorageSystem] Polling attempt ${attempt} for ${promptId}: Output not ready yet.`);

        } catch (error) {
            console.error(`[StorageSystem] Polling attempt ${attempt} failed for ${promptId}:`, error);
            const isNotFoundError = error.response && error.response.status === 404;
            if (attempt >= maxAttempts || isNotFoundError) {
                 const finalMessage = isNotFoundError ? 'History not found (potentially expired).' : `Polling timed out after ${attempt} attempts.`;
                 throw new Error(`Polling failed for ${promptId}: ${finalMessage}`);
            }
            // Allow polling to continue for other errors (e.g., transient network issues)
        }
    } // end while loop

    // If loop finishes without finding output or throwing a terminal error
    throw new Error(`[StorageSystem] Polling finished for prompt ${promptId} without finding output after ${maxAttempts} attempts.`);
}


/**
 * Saves the project metadata to the Nhost backend using a GraphQL mutation.
 * @param {object} projectData - Object containing required project details.
 * @returns {Promise<string>} ID of the saved project document.
 * @throws {Error} If required project data is missing or GraphQL mutation fails.
 */
async function saveProjectMetadata(projectData) {
  // Validate required fields before saving
  const requiredFields = ['userId', 'promptId', 'outputUrl', 'outputR2Key', 'toolId', 'originalFileName'];
  const missingField = requiredFields.find(field => !projectData[field]);
  if (missingField) {
    console.error("[StorageSystem] Missing required field for saving project metadata:", missingField, projectData);
    throw new Error(`Cannot save project metadata: Missing required field '${missingField}'.`);
  }

  console.log("[StorageSystem] Saving project metadata via GraphQL:", projectData);
  try {
    // Use nhost.graphql.request for the mutation
    const { data, error } = await nhost.graphql.request(INSERT_PROJECT_MUTATION, {
       // Map projectData fields to mutation variables
       userId: projectData.userId,
       promptId: projectData.promptId,
       outputUrl: projectData.outputUrl,
       outputR2Key: projectData.outputR2Key,
       toolId: projectData.toolId,
       originalFileName: projectData.originalFileName,
       promptUsed: projectData.promptUsed,
       contentType: projectData.contentType,
       comfyOutputFileName: projectData.comfyOutputFileName,
       outputType: projectData.outputType,
       // Add optional fields if they exist in projectData and mutation
       // creditCost: projectData.creditCost,
       // generationType: projectData.generationType,
    });

    if (error) {
      console.error("[StorageSystem] GraphQL mutation error saving project metadata:", error);
      throw new Error(error.message || 'Failed to save project metadata.');
    }

    const projectId = data?.insert_projects_one?.id;
    if (!projectId) {
        console.error("[StorageSystem] Project metadata mutation succeeded but returned no ID.");
        throw new Error("Failed to save project metadata (no ID returned).");
    }

    console.log("[StorageSystem] Project metadata saved successfully via GraphQL, ID:", projectId);
    return projectId;
  } catch (err) {
    console.error("[StorageSystem] Unexpected error saving project metadata:", err);
    throw new Error(`Failed to save project: ${err.message}`);
  }
}

// --- Export the Public Interface ---
export const StorageSystem = {
  getComfyInstanceUrl,      // Now uses GraphQL request
  getWorkflowTemplate,
  uploadInputToComfy,
  logPromptAttempt,         // Uses GraphQL request
  submitWorkflowToComfy,
  pollComfyOutputAndSave,
  saveProjectMetadata,      // Uses GraphQL request
};
